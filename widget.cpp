/*
 * Widget.cpp - Flappy Bird游戏主窗口类实现文件
 *
 * 实现细节：
 * 1. 窗口初始化和配置
 * 2. 游戏场景的创建和设置
 * 3. 背景图片的加载和定位
 * 4. Graphics View框架的集成
 */

#include "widget.h"
#include "ui_widget.h"          // UI类定义，由Qt Designer自动生成
#include <QGraphicsPixmapItem>  // 图形项类，用于显示图片
#include <QIcon>                // 窗口图标类

/**
 * @brief Widget构造函数 - 游戏窗口的完整初始化过程
 * @param parent 父窗口指针
 *
 * 初始化流程详解：
 * 1. 调用父类构造函数，建立Qt对象层次结构
 * 2. 创建UI对象并设置到当前窗口
 * 3. 配置窗口属性（标题、图标）
 * 4. 创建游戏场景并设置尺寸
 * 5. 加载背景图片并添加到场景
 * 6. 初始化小鸟对象
 * 7. 将场景绑定到图形视图组件
 */
Widget::Widget(QWidget *parent)
    : QWidget(parent)           // 调用父类构造函数，建立Qt对象树
    , ui(new Ui::Widget)        // 创建UI对象，包含界面布局和控件
{
    // === 第一步：UI界面初始化 ===
    ui->setupUi(this);          // 将UI文件中定义的界面应用到当前窗口
                                // 这会创建所有控件并设置布局

    // === 第二步：窗口属性设置 ===
    setWindowTitle("FlappyBird");                                    // 设置窗口标题
    setWindowIcon(QIcon(":/new/prefix1/logo.ico"));                // 设置窗口图标
                                                                    // 使用Qt资源系统加载图标

    // === 第三步：游戏场景创建 ===
    scene = new Scene(this);    // 创建游戏场景对象，this作为父对象确保内存管理
                                // Scene继承自QGraphicsScene，是游戏逻辑的核心

    // === 第四步：场景尺寸设置 ===
    scene->setSceneRect(0,0,432,644);  // 设置场景的坐标系统和可视区域
                                       // (0,0) 为左上角，432x644为游戏窗口尺寸
                                       // 这个尺寸与UI文件中QGraphicsView的尺寸对应

    // === 第五步：背景图片设置 ===
    QGraphicsPixmapItem* pixItem = new QGraphicsPixmapItem(QPixmap(":/new/prefix1/bg.png"));
    // 创建图形项对象，加载背景图片
    // QGraphicsPixmapItem是Qt Graphics View框架中用于显示图片的标准类

    scene->addItem(pixItem);    // 将背景图片添加到场景中
    pixItem->setPos(0,0);       // 设置背景图片位置为场景左上角
                                // 背景图片将作为游戏的静态背景

    // === 第六步：游戏对象初始化 ===
    scene->birddef();           // 调用场景的小鸟定义方法
                                // 这会创建小鸟对象并添加到场景中

    // === 第七步：View-Scene绑定 ===
    ui->Box->setScene(scene);   // 将游戏场景绑定到UI中的QGraphicsView控件
                                // Box是在UI文件中定义的QGraphicsView对象名
                                // 这建立了Model-View架构中的View-Model连接
}

/**
 * @brief Widget析构函数 - 资源清理
 *
 * 清理说明：
 * 1. 只需要删除ui指针，因为它不在Qt对象树中
 * 2. scene对象由于设置了parent为this，会被Qt自动清理
 * 3. Qt的父子对象系统确保所有子对象都会被正确释放
 */
Widget::~Widget()
{
    delete ui;  // 释放UI对象内存
                // scene等其他对象由Qt的父子对象系统自动管理
}